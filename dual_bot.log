🤖 Telegram Bot 启动中...
📅 启动时间: Wed Jul 30 19:58:16 CEST 2025
📂 工作目录: /home/<USER>
🐍 Python版本: Python 3.12.3
📦 虚拟环境: /home/<USER>/venv
📡 监听端口: 5000
================================
2025-07-30 19:58:18,805 - nyxtrade.trader - INFO - OKX交易所连接初始化成功，同步包装器已初始化
2025-07-30 19:58:18,806 - __main__ - INFO - 🚀 启动 Telegram Bot 服务...
2025-07-30 19:58:18,807 - __main__ - INFO - 初始化 Telegram Bot...
2025-07-30 19:58:18,816 - utils.db_utils - INFO - 用户表创建成功或已存在
2025-07-30 19:58:18,817 - __main__ - INFO - MySQL用户表创建成功或已存在
2025-07-30 19:58:18,967 - __main__ - INFO - 新闻bot实例创建成功
2025-07-30 19:58:19,043 - __main__ - INFO - 交易bot实例创建成功
2025-07-30 19:58:19,044 - __main__ - INFO - 新闻bot命令处理器注册完成
2025-07-30 19:58:19,044 - __main__ - INFO - 使用标准命令处理器
2025-07-30 19:58:19,044 - __main__ - INFO - 交易bot命令处理器注册完成
2025-07-30 19:58:19,080 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/getMe "HTTP/1.1 200 OK"
2025-07-30 19:58:19,081 - __main__ - INFO - 新闻bot Application 初始化成功
2025-07-30 19:58:19,107 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7944631563:AAGfLf2Cd-T20jSDnsSnlX5c8AI-Cp-mq_Y/getMe "HTTP/1.1 200 OK"
2025-07-30 19:58:19,108 - __main__ - INFO - 交易bot Application 初始化成功
2025-07-30 19:58:19,285 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/setMyCommands "HTTP/1.1 200 OK"
2025-07-30 19:58:19,286 - __main__ - INFO - 新闻bot命令菜单设置成功
2025-07-30 19:58:19,565 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7944631563:AAGfLf2Cd-T20jSDnsSnlX5c8AI-Cp-mq_Y/setMyCommands "HTTP/1.1 200 OK"
2025-07-30 19:58:19,566 - __main__ - INFO - 交易bot基础命令菜单设置成功
2025-07-30 19:58:19,574 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/getMe "HTTP/1.1 200 OK"
2025-07-30 19:58:19,574 - __main__ - INFO - 新闻Bot信息: nyxn_bot (ID: 7781242712)
2025-07-30 19:58:19,582 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7944631563:AAGfLf2Cd-T20jSDnsSnlX5c8AI-Cp-mq_Y/getMe "HTTP/1.1 200 OK"
2025-07-30 19:58:19,583 - __main__ - INFO - 交易Bot信息: NyxTrade_bot (ID: 7944631563)
2025-07-30 19:58:19,583 - __main__ - INFO - 设置 Webhook...
2025-07-30 19:58:19,589 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/deleteWebhook "HTTP/1.1 200 OK"
2025-07-30 19:58:19,590 - __main__ - INFO - 新闻bot旧的 Webhook 已删除。
2025-07-30 19:58:19,614 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/setWebhook "HTTP/1.1 200 OK"
2025-07-30 19:58:19,623 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7781242712:AAGbVYEeHrdiDB7rjdoO3cahSpaCiRT59lg/getWebhookInfo "HTTP/1.1 200 OK"
2025-07-30 19:58:19,624 - __main__ - INFO - 新闻bot Webhook 已成功设置到: https://hook.nyxn.ai/webhook/news
2025-07-30 19:58:19,625 - __main__ - INFO - 新闻bot Webhook 信息: WebhookInfo(has_custom_certificate=False, ip_address='*************', max_connections=40, pending_update_count=0, url='https://hook.nyxn.ai/webhook/news')
2025-07-30 19:58:19,635 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7944631563:AAGfLf2Cd-T20jSDnsSnlX5c8AI-Cp-mq_Y/deleteWebhook "HTTP/1.1 200 OK"
2025-07-30 19:58:19,635 - __main__ - INFO - 交易bot旧的 Webhook 已删除。
2025-07-30 19:58:19,644 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7944631563:AAGfLf2Cd-T20jSDnsSnlX5c8AI-Cp-mq_Y/setWebhook "HTTP/1.1 200 OK"
2025-07-30 19:58:19,653 - httpx - INFO - HTTP Request: POST https://api.telegram.org/bot7944631563:AAGfLf2Cd-T20jSDnsSnlX5c8AI-Cp-mq_Y/getWebhookInfo "HTTP/1.1 200 OK"
2025-07-30 19:58:19,654 - __main__ - INFO - 交易bot Webhook 已成功设置到: https://hook.nyxn.ai/webhook/trading
2025-07-30 19:58:19,654 - __main__ - INFO - 交易bot Webhook 信息: WebhookInfo(has_custom_certificate=False, ip_address='**************', max_connections=40, pending_update_count=0, url='https://hook.nyxn.ai/webhook/trading')
2025-07-30 19:58:19,654 - __main__ - INFO - ✅ Telegram Bot 初始化完成，准备启动服务器...
2025-07-30 19:58:19,656 - __main__ - INFO - 🌐 启动 Web 服务器，监听端口 5000
2025-07-30 19:58:19,661 - __main__ - INFO - 启动 Flask 服务器，监听 0.0.0.0:5000
 * Serving Flask app 'main'
 * Debug mode: off
2025-07-30 19:58:19,667 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:5000
 * Running on http://***************:5000
2025-07-30 19:58:19,667 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-07-30 19:58:32,519 - werkzeug - INFO - 127.0.0.1 - - [30/Jul/2025 19:58:32] "GET /health HTTP/1.1" 200 -
2025-07-30 19:59:16,701 - werkzeug - INFO - 127.0.0.1 - - [30/Jul/2025 19:59:16] "GET /health HTTP/1.1" 200 -
